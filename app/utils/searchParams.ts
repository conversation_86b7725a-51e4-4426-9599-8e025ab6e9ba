export const setSearchParams = (
  current: string,
  searchKey: string,
  value: string,
  clearParams?: string[]
) => {
  const searchParams = new URLSearchParams(current)

  // Clear specified parameters if provided
  if (clearParams) {
    clearParams.forEach((param) => searchParams.delete(param))
  }

  if (value.length) {
    searchParams.set(searchKey, value)
  } else {
    searchParams.delete(searchKey)
  }
  return searchParams.toString()
}
