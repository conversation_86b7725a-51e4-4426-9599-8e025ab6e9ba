import { LoaderFunction, MetaFunction } from '@remix-run/node'
import { useLoaderData } from '@remix-run/react'
import {
  PromotionEntity,
  ReviewEntity,
  ServiceContentBasic,
  ServiceContentBoxes,
  ServiceContentDetail,
  ServiceContentDynamic,
  ServiceContentImage,
  ServiceEntity,
  ServicePromotion,
  ServiceReview,
  ServiceSocial,
  ServiceVdo,
} from '@/lib/api/entity'
import { fetchServiceBySlug, queryStringBuilder } from '@/utils/fetchHelper'
import { buildTags } from '@/lib/server/seo/seo-utils'
import { defineRoute, withCors } from '@/utils/routeHelper'
import { ServiceContentImageLayout } from '@/components/strapi/layout/service-content-Image'
import { ServiceContentBasicLayout } from '@/components/strapi/layout/service-content-basic'
import { ServiceContentBoxesLayout } from '@/components/strapi/layout/service-content-boxes'
import { ServiceContentDetailLayout } from '@/components/strapi/layout/service-content-detail'
import { ServiceVdoLayout } from '@/components/strapi/layout/service-vdo'
import { ServiceHeaderLayout } from '@/components/strapi/layout/service-header'
import { ServiceQuestionAnswerLayout } from '@/components/strapi/layout/service-question-answer'
import { StrapiImage } from '@/components/strapi/strapi-image'
import { StrapiFields } from '@/lib/api/types'
import { ServicePromotionLayout } from '@/components/strapi/layout/service-promotion'
import { ServiceSocialLayout } from '@/components/strapi/layout/service-social'
import { ServiceReviewLayout } from '@/components/strapi/layout/service-review'
import { ServiceContentDynamicLayout } from '@/components/strapi/layout/service-content-dynamic'
import { HelmetSEO } from '@/components/helmet/helmet-seo'
import { PopupPreviewDialog } from '@/components/dialog'

type LoaderData = {
  dataService: ServiceEntity
  locale: string
  slug: string
}

type ServiceLayoutProps = {
  data?: Array<
    | ServiceContentImage
    | ServiceContentDetail
    | ServiceContentBoxes
    | ServiceContentBasic
    | ServiceVdo
    | ServiceReview
    | ServicePromotion
    | ServiceSocial
  > | null
  promotions?: StrapiFields<PromotionEntity> | null
  reviews?: StrapiFields<ReviewEntity> | null
  slug: string
  locale: string
}

export const meta: MetaFunction = ({ data }) => {
  const { dataService } = data as LoaderData
  return buildTags(dataService.seo)
}

export const loader: LoaderFunction = async ({ request }) => {
  withCors()
  const { slug, lng } = defineRoute(request)

  const dataService = await fetchServiceBySlug(
    slug,
    lng,
    queryStringBuilder([
      'seo',
      'seo.metaSocial',
      'content',
      'bannerImage',
      // PopupPreview
      'popupPreview',
      'popupPreview.image',
      'popupPreview.buttonImage',
      // ServiceHeader
      'header',
      'header.image',
      // ServiceContentImage
      'content.image1',
      'content.image2',
      // ServiceContentDetail, ServiceContentBoxes, ServiceContentBasic
      'content.images',
      'content.images.image',
      // ServiceSocial
      'content.links',
      // ServiceQuestionAnswer
      'questionAnswer',
      // ServicePromotion
      'promotions',
      'promotions.image',
      // ServiceReview
      'reviews',
      'reviews.image',
      'content.buttonImage',
      'content.backgroundColor',
      'content.backgroundImage',
    ])
  )

  return {
    dataService,
    locale: lng,
    slug,
  }
}

const ServiceLayout = ({
  data,
  promotions,
  slug,
  reviews,
  locale,
}: ServiceLayoutProps) => {
  if (!data) return null

  const defineLayout = (
    item:
      | ServiceContentImage
      | ServiceContentDetail
      | ServiceContentBoxes
      | ServiceContentBasic
      | ServiceVdo
      | ServiceReview
      | ServicePromotion
      | ServiceSocial
  ) => {
    switch (item.__component.toLowerCase()) {
      case 'layout.service-content-dynamic':
        return (
          <ServiceContentDynamicLayout
            key={`${slug}-${item.__component}-${item.id}-${item.type}`}
            data={item as ServiceContentDynamic}
          />
        )
      case 'layout.service-content-image':
        return (
          <ServiceContentImageLayout
            key={`${slug}-${item.__component}-${item.id}-${item.type}`}
            data={item as ServiceContentImage}
          />
        )
      case 'layout.service-content-basic':
        return (
          <ServiceContentBasicLayout
            key={`${slug}-${item.__component}-${item.id}-${item.type}`}
            data={item as ServiceContentBasic}
            slug={slug}
          />
        )
      case 'layout.service-content-boxes':
        return (
          <ServiceContentBoxesLayout
            key={`${slug}-${item.__component}-${item.id}-${item.type}`}
            data={item as ServiceContentBoxes}
            slug={slug}
          />
        )
      case 'layout.service-content-detail':
        return (
          <ServiceContentDetailLayout
            key={`${slug}-${item.__component}-${item.id}-${item.type}`}
            data={item as ServiceContentDetail}
            slug={slug}
          />
        )
      case 'layout.service-promotion': {
        const temp = item as ServicePromotion
        temp.promotions = promotions
        return (
          <ServicePromotionLayout
            key={`${slug}-${item.__component}-${item.id}-${item.type}`}
            data={temp}
            slug={slug}
          />
        )
      }
      case 'layout.service-social':
        return (
          <ServiceSocialLayout
            key={`${item.__component}-${item.id}-${item.type}`}
            data={item as ServiceSocial}
            slug={slug}
          />
        )
      case 'layout.video-content':
        return (
          <ServiceVdoLayout
            key={`${item.__component}-${item.id}-${item.type}`}
            data={item as ServiceVdo}
          />
        )
      case 'layout.service-review': {
        const temp = item as ServiceReview
        temp.reviews = reviews
        return (
          <ServiceReviewLayout
            key={`${item.__component}-${item.id}-${item.type}`}
            data={temp}
            locale={locale}
            slug={slug}
          />
        )
      }

      default:
        return null
    }
  }

  return data.map(defineLayout)
}

export default function ServicePage() {
  const { dataService, slug, locale } = useLoaderData<LoaderData>()
  const {
    bannerImage,
    header,
    content,
    promotions,
    reviews,
    questionAnswer,
    popupPreview,
  } = dataService
  return (
    <>
      <HelmetSEO data={dataService.seo} />
      <StrapiImage
        value={bannerImage}
        className="animate-in fade-in duration-500 w-full h-fit"
      />
      <ServiceHeaderLayout data={header} slug={slug} />
      <ServiceLayout
        data={content}
        promotions={promotions}
        reviews={reviews}
        slug={slug}
        locale={locale}
      />
      {!!questionAnswer?.length && (
        <ServiceQuestionAnswerLayout data={questionAnswer} slug={slug} />
      )}
      {popupPreview && <PopupPreviewDialog data={popupPreview} />}
    </>
  )
}
