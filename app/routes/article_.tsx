import { ArticleDropdown, Container } from '@/components'
import SearchInput from '@/components/searchInput'
import { ArticleEntity, PageEntity, ServiceEntity } from '@/lib/api/entity'
import { buildTags } from '@/lib/server/seo/seo-utils'
import { fetchPageBySlug, queryStringBuilder } from '@/utils/fetchHelper'
import { LoaderFunction, MetaFunction } from '@remix-run/node'
import { Link, useLoaderData } from '@remix-run/react'
import { strapiGetServices } from '@/lib/api/strapi/service'
import { strapiGetArticles } from '@/lib/api/strapi/article'
import {
  warperCollectionType,
  warperCollectionTypePagination,
} from '@/utils/strapiHelper'
import { useTranslation } from 'react-i18next'
import { cn } from '@/lib/utils'
import { StrapiImage } from '@/components/strapi/strapi-image'
import { i18nConfig } from '@/lib/server/translations/i18n.config'
import { getLocaleParam } from '@/utils/localeHelper'
import { HelmetSEO } from '@/components/helmet/helmet-seo'
import { withCors } from '@/utils/routeHelper'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { Pagination } from '@/lib/api/types'
import { Loader } from '@/components/loader'
import { Button } from '@/components/ui/button'
import { ChevronRightIcon } from 'lucide-react'
import { useInView } from 'react-intersection-observer'

type LoaderData = {
  dataPage: PageEntity
  dataService: ServiceEntity[]
  locale: string
  search: string
  filtered: string
}

export const meta: MetaFunction = ({ data }) => {
  const { dataPage } = data as LoaderData
  return buildTags(dataPage.seo)
}

export const loader: LoaderFunction = async ({ request }) => {
  withCors()
  const url = new URL(request.url)
  const slug = url.pathname.split('/').filter(Boolean).pop()
  const search = url.searchParams.get('search') ?? ''
  const filtered = url.searchParams.get('filtered') ?? ''
  const lng = url.searchParams.get('lng') ?? i18nConfig.fallbackLng

  const [responsePage, responseServices] = await Promise.all([
    fetchPageBySlug(
      slug,
      lng,
      queryStringBuilder([
        'backgroundImage',
        'seo',
        'seo.metaSocial',
        'seo.metaImage',
      ])
    ),
    strapiGetServices(
      queryStringBuilder(['articles'], undefined, ['slug']),
      lng
    ),
  ])

  return {
    dataPage: responsePage,
    dataService: warperCollectionType(responseServices),
    locale: lng,
    search,
    filtered,
  }
}

export default function ArticlePage(): React.JSX.Element {
  const { dataPage, dataService, search, filtered, locale } =
    useLoaderData<LoaderData>()
  const { t } = useTranslation('article')
  const { t: home } = useTranslation('home')
  const [articles, setArticles] = useState<ArticleEntity[]>([])
  const [pagination, setPagination] = useState<Pagination | undefined>()
  const [isFetching, setIsFetching] = useState<boolean>(false)

  const hasMore = useMemo(
    () =>
      !!pagination?.page &&
      !!pagination?.pageCount &&
      pagination?.page < pagination?.pageCount,
    [pagination?.page, pagination?.pageCount]
  )

  const { ref, entry } = useInView({
    delay: 100,
    skip: !hasMore,
    trackVisibility: true,
    threshold: 1,
  })

  const fetchArticles = useCallback(
    async (currPage: number | undefined) => {
      setIsFetching(true)
      const response = await strapiGetArticles(
        queryStringBuilder(
          ['image', 'service'],
          {
            $and: [
              filtered?.length ? { service: { slug: { $eq: filtered } } } : {},
              { title: { $containsi: decodeURI(search) } },
            ],
          },
          undefined,
          {
            page: currPage ? currPage + 1 : 1,
            pageSize: 15,
          }
        ),
        locale
      )
      const { data, pagination: resPagination } =
        warperCollectionTypePagination(response)
      setArticles((prev) => [...prev, ...(data ?? [])])
      setPagination(resPagination)

      setIsFetching(false)
    },
    [filtered, locale, search]
  )

  const ArticleList = useMemo(
    () => (
      <Container
        className={cn({
          'grid grid-cols-1 sm:grid-cols-2 gap-8 gap-x-14': articles.length,
          'flex-1 py-6 lg:py-12 flex justify-center': !articles.length,
        })}
      >
        {!!articles.length &&
          !!pagination?.page &&
          articles.map((article) => (
            <Link
              key={`article-${article.title}-${article.slug}-${article.image?.data?.attributes?.name}`}
              to={{
                pathname: `/article/${encodeURIComponent(article.slug)}`,
                search: getLocaleParam(locale),
              }}
              className="flex flex-col h-full"
            >
              <StrapiImage
                className="rounded-xl shadow-lg shadow-primary-400 !bg-contain"
                value={article.image}
                classNameFallBack="bg-image-coming w-full h-full aspect-[3/2] !bg-contain"
              />
              <div className="h-full my-8 flex flex-col space-y-8 justify-between">
                <div className="flex flex-col space-y-2">
                  <h4 className="text-primary line-clamp-2">{article.title}</h4>
                  <p className="text-xs !text-gray-600">{article.subtitle}</p>
                </div>
                <Button size="xs" className="w-fit font-normal text-xs">
                  {t('readMore')}
                </Button>
              </div>
            </Link>
          ))}

        {!!pagination?.page && !articles.length && !isFetching && (
          <h2 className="animate-in zoom-in duration-500 text-primary">
            {search.length ? `${t('notFound')} "${search}"` : t('noArticle')}
          </h2>
        )}
        {isFetching && (
          <div className="flex justify-center items-center w-full">
            <Loader />
          </div>
        )}
        {hasMore && !isFetching && (
          <div className="w-full col-span-full flex p-10 justify-center">
            <Button
              className="max-md:hidden"
              disabled={isFetching}
              onClick={() => fetchArticles(pagination?.page)}
            >
              {home('promotion.seeMore')}
              <div className="chevron-btn">
                <ChevronRightIcon className="w-4 h-4 text-white" />
              </div>
            </Button>

            <div className="md:hidden" ref={ref} />
          </div>
        )}
      </Container>
    ),
    [
      articles,
      fetchArticles,
      hasMore,
      home,
      isFetching,
      locale,
      pagination?.page,
      ref,
      t,
      search,
    ]
  )

  useEffect(() => {
    if (entry?.isIntersecting && !isFetching && hasMore) {
      fetchArticles(pagination?.page)
    }
  }, [
    entry?.isIntersecting,
    fetchArticles,
    hasMore,
    isFetching,
    pagination?.page,
  ])

  useEffect(() => {
    if (search.length && filtered.length) return
    setPagination((prev) => (prev ? { ...prev, page: 0 } : undefined))
    setArticles([])
    fetchArticles(undefined)
  }, [search, filtered, fetchArticles])

  return (
    <div className="w-full flex flex-col pb-6">
      <HelmetSEO data={dataPage.seo} />
      {dataPage.backgroundImage?.data && (
        <StrapiImage
          value={dataPage.backgroundImage}
          className="animate-in fade-in duration-500 w-full h-fit"
        />
      )}

      <Container>
        <div className="flex justify-between py-6 gap-4">
          <ArticleDropdown services={dataService} />
          <SearchInput
            className="h-10"
            defaultValue={search}
            isLink
            placeholder={`${t('searchPlaceholder')}...`}
            setIsFetching={setIsFetching}
            setArticles={setArticles}
            isPending={isFetching}
          />
        </div>
      </Container>
      {ArticleList}
    </div>
  )
}
