import { Container } from '@/components'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@/components/ui/carousel'
import { ImageType, PromotionEntity } from '@/lib/api/entity'
import { Link } from '@remix-run/react'
import { FC, useMemo } from 'react'
import { StrapiImage } from '../../strapi-image'
import { SOCIAL_MEDIA } from '@/constants/social'
import { useTranslation } from 'react-i18next'
import { StrapiField } from '@/lib/api/types'
import { ServicePromotionProps } from '.'

type ServicePromotionTypeOneProps = ServicePromotionProps

interface PromotionCarouselProps {
  data: PromotionEntity[]
  slug: string
  hasMultiplePromotions: boolean
}

const PromotionHeader = ({
  backgroundColor,
  buttonImage,
}: {
  backgroundColor: string | null
  buttonImage: StrapiField<ImageType> | ImageType
}): React.ReactElement => {
  const { t } = useTranslation('promotion')

  return (
    <div
      className="flex w-full md:flex-col text-primary gap-4 max-md:justify-between"
      style={
        backgroundColor
          ? {
              color: backgroundColor,
            }
          : {}
      }
    >
      <div
        className="border-l-4 border-primary pl-4"
        style={
          backgroundColor
            ? {
                borderColor: backgroundColor,
              }
            : {}
        }
      >
        <h2 className="text-md lg:text-5xl">{t('reversedTitle')}</h2>
        <h4>{t('title')}</h4>
      </div>
      <div className="button-image ">
        <Link to={SOCIAL_MEDIA.LINE}>
          <StrapiImage
            value={buttonImage}
            className="max-w-50 lg:max-w-60 rounded-full object-cover"
          />
        </Link>
      </div>
    </div>
  )
}

const PromotionCarousel: FC<PromotionCarouselProps> = ({
  data,
  slug,
  hasMultiplePromotions,
}) => (
  <div className="relative">
    <Carousel
      className="w-full text-center"
      opts={{ active: hasMultiplePromotions, align: 'start' }}
    >
      <CarouselContent>
        {data.map(({ createdAt, id, image }) => (
          <CarouselItem
            key={`service-promotion-${slug}-${id}-${createdAt}-${image?.data?.attributes?.name}`}
          >
            <StrapiImage
              value={image}
              className="aspect-square rounded-3xl object-cover bg-primary-200 max-w-72 lg:max-w-xl"
            />
          </CarouselItem>
        ))}
      </CarouselContent>
      <CarouselPrevious className="text-primary" />
      <CarouselNext className="text-primary" />
    </Carousel>
  </div>
)

export const ServicePromotionLayoutTypeOne: FC<
  ServicePromotionTypeOneProps
> = ({ backgroundColor, buttonImage, data, slug }) => {
  const hasMultiplePromotions = useMemo(() => data.length > 1, [data])

  return (
    <div className="bg-secondary/10 w-full py-8 lg:py-24">
      <Container className="flex flex-col md:flex-row gap-3 lg:gap-6 justify-between">
        <div className="flex-1">
          <PromotionHeader
            backgroundColor={backgroundColor}
            buttonImage={buttonImage}
          />
        </div>
        {data && (
          <div className="flex-1">
            <PromotionCarousel
              data={data}
              slug={slug}
              hasMultiplePromotions={hasMultiplePromotions}
            />
          </div>
        )}
      </Container>
    </div>
  )
}
