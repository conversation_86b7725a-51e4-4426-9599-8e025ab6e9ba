import { Button } from '@/components/ui/button'
import { ServiceCardItem } from '../../routes/our-service_'
import { StrapiImage } from '@/components/strapi/strapi-image'
import { useTranslation } from 'react-i18next'
import { Link } from '@remix-run/react'
import { cn } from '@/lib/utils'
import { getLocaleParam } from '@/utils/localeHelper'

interface ServiceCardProps {
  data: ServiceCardItem
  locale: string
}

const ServicesCard: React.FC<ServiceCardProps> = ({ data, locale }) => {
  const { t } = useTranslation('home')
  return (
    <div className="bg-lemon-200 flex flex-col md:flex-row rounded-xl md:rounded-[50px] md:shadow-[1px 1px 1px 1px grey] w-4/5 max-h-44">
      <Link
        className="relative bg-brown-200 shadow-xl overflow-hidden text-white flex flex-col justify-center items-center text-start rounded-xl md:rounded-[50px] md:shadow-[1px 1px 1px 1px grey] h-40 md:h-auto md:flex-1 w-full max-w-[350]"
        to={{
          pathname: encodeURIComponent(data.link ?? ''),
          search: getLocaleParam(locale),
        }}
      >
        {data.image?.data ? (
          <StrapiImage
            className="w-full h-full relative object-cover"
            value={data.image}
          />
        ) : (
          <h2>{data.name}</h2>
        )}
      </Link>

      <div
        className={cn(
          'justify-between p-4 md:w-[65%] md:m-0 md:px-12 md:py-6 text-white flex-1 h-44 hidden md:flex md:flex-col',
          data.description ?? 'justify-content-end'
        )}
      >
        {data.description && (
          <div
            className="p-line-clamp-3 mt-3 text-force ck ck-content"
            dangerouslySetInnerHTML={{ __html: data.description }}
          />
        )}
        <Link
          to={{
            pathname: encodeURIComponent(data.link ?? ''),
            search: getLocaleParam(locale),
          }}
        >
          <Button className="mt-4 w-fit bg-white text-primary" variant="ghost">
            {t('aboutUs.more')}
          </Button>
        </Link>
      </div>
    </div>
  )
}

export { ServicesCard }
