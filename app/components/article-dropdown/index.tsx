import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { ComponentPropsWithoutRef, useCallback, useMemo } from 'react'
import { buttonVariants } from '@/components/ui/button'
import { ChevronDownCircle } from 'lucide-react'
import { cn } from '@/lib/utils'
import { ServiceEntity } from '@/lib/api/entity'
import { Link, useLocation } from '@remix-run/react'
import { useTranslation } from 'react-i18next'
import { useLocale } from 'remix-i18next/react'
import { warperCollectionType } from '@/utils/strapiHelper'
import useClient from '@/hooks/use-client'

type ArticleDropdownProps = ComponentPropsWithoutRef<typeof DropdownMenu> & {
  services: ServiceEntity[]
}

export function ArticleDropdown({ services, ...props }: ArticleDropdownProps) {
  const locale = useLocale()
  const { isClient } = useClient()
  const { search } = useLocation()
  const { t } = useTranslation('article')

  const setSearch = useCallback(
    (value: string) => {
      const searchParams = new URLSearchParams(search)
      searchParams.delete('search')
      if (value.length) {
        searchParams.set('filtered', value)
      } else {
        searchParams.delete('filtered')
      }

      return searchParams.toString()
    },
    [search]
  )

  const selectedService = useMemo(() => {
    return services.find(
      (service) => service?.slug && search.includes(service?.slug)
    )
  }, [search, services])

  return (
    <DropdownMenu {...props}>
      <DropdownMenuTrigger
        className={cn(
          buttonVariants(),
          'gap-x-2 group focus-visible:ring-0 !min-w-48 justify-between'
        )}
        disabled={!isClient}
      >
        {selectedService ? selectedService.name : t('filterTitle')}
        <ChevronDownCircle className="size-6 shrink-0 transition-transform group-data-[state=open]:rotate-180" />
      </DropdownMenuTrigger>
      <DropdownMenuContent className="!w-[--radix-dropdown-menu-trigger-width] bg-primary text-white h-full max-h-44 overflow-y-scroll">
        {services?.length ? (
          <>
            <DropdownMenuItem
              asChild
              className={cn('hover:bg-primary-100 cursor-pointer', {
                'pointer-events-none bg-secondary-300':
                  !search.includes('filtered'),
              })}
              key={`service-option-all`}
            >
              <Link
                to={{
                  search: setSearch(''),
                }}
                preventScrollReset
              >
                {t('all')}
              </Link>
            </DropdownMenuItem>
            {services?.map((service) => {
              const localizeService = service.localizations
                ? warperCollectionType(service.localizations)?.pop()
                : undefined
              return (
                <DropdownMenuItem
                  asChild
                  className={cn('hover:bg-primary-100 cursor-pointer', {
                    'pointer-events-none bg-secondary-300':
                      service.slug && search.includes(service.slug),
                  })}
                  key={`service-option-${service.slug}`}
                >
                  <Link
                    to={{
                      search: setSearch(service.slug ?? ''),
                    }}
                    preventScrollReset
                  >
                    {locale !== 'eng' && localizeService
                      ? localizeService.name
                      : service.name}
                  </Link>
                </DropdownMenuItem>
              )
            })}
          </>
        ) : (
          <p>{t('noData')}</p>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
