import React from 'react'
import { cn } from '@/lib/utils'
import usePrevious from '@/hooks/use-previous'
import { useLocation, useNavigate } from '@remix-run/react'
import { Button } from '@/components/ui/button'
import { Loader, SearchIcon } from 'lucide-react'
import { setSearchParams } from '@/utils/searchParams'
import { ArticleEntity } from '@/lib/api/entity'
import { XMarkIcon } from '@heroicons/react/24/outline'

type SearchInputProps = React.ComponentPropsWithoutRef<'form'> &
  React.ComponentPropsWithoutRef<'input'> & {
    defaultValue?: string
    handleSubmit?: (value: string) => void
    isFullWidth?: boolean
    isLink?: boolean
    isManualReset?: boolean
    isPending?: boolean
    searchParam?: string
    setIsFetching?: (value: boolean) => void
    setArticles?: (value: ArticleEntity[]) => void
  }

export default function SearchInput({
  className,
  defaultValue = '',
  handleSubmit,
  isFullWidth,
  isLink,
  isManualReset,
  isPending,
  name = 'search',
  placeholder,
  searchParam = 'search',
  setIsFetching,
  setArticles,
}: SearchInputProps) {
  const { search } = useLocation()
  const navigate = useNavigate()
  const [value, setValue] = React.useState<string>(defaultValue)
  const previous = usePrevious(value)
  const ref = React.useRef<HTMLInputElement>(null)

  const handleReset = React.useCallback(() => {
    setValue('')
    handleSubmit?.('')
  }, [handleSubmit])

  const onSubmit = React.useCallback(
    (e: React.FormEvent<HTMLFormElement>) => {
      e.preventDefault()
      handleSubmit?.(value)
      if (isLink) {
        navigate(
          {
            search: setSearchParams(search, searchParam, value),
          },
          {
            preventScrollReset: true,
          }
        )
        setArticles?.([])
        setIsFetching?.(true)
      }
    },
    [
      handleSubmit,
      isLink,
      navigate,
      search,
      searchParam,
      value,
      setIsFetching,
      setArticles,
    ]
  )

  React.useEffect(() => {
    if (previous && previous?.trim().length > 0 && value.trim().length < 1)
      handleReset()
  }, [handleReset, previous, value])

  React.useEffect(() => {
    if (isManualReset) handleReset()
  }, [handleReset, isManualReset])

  return (
    <form
      className={cn(
        'flex h-9 items-center space-x-0 rounded-full overflow-clip border border-input bg-white pl-4',
        'focus-within:outline-none focus-within:ring-2 focus-within:ring-primary cursor-text',
        {
          'w-56': !isFullWidth,
          'w-full': isFullWidth,
        },
        className
      )}
      onFocus={() => ref.current?.focus()}
      onSubmit={onSubmit}
    >
      <input
        ref={ref}
        name={name}
        autoComplete="off"
        placeholder={placeholder}
        value={value}
        onChange={(e) => setValue(e.target.value)}
        type="text"
        className="w-[calc(100%-1.5rem)] h-full text-sm bg-transparent focus:outline-none focus:border-none"
        disabled={isPending}
      />
      {value && !isPending && (
        <XMarkIcon
          className="size-4 shrink-0 cursor-pointer text-secondary"
          strokeWidth={2}
          onClick={handleReset}
          aria-label="Clear"
        />
      )}

      <Button
        disabled={isPending}
        variant="ghost"
        size="sm"
        className="h-full rounded-l-none rounded-r-md focus-visible:ring-2 focus-visible:ring-blue-500"
      >
        {isPending ? (
          <Loader className="size-4 shrink-0 animate-spin" />
        ) : (
          <SearchIcon className="size-4 shrink-0 text-primary" />
        )}
      </Button>
    </form>
  )
}
